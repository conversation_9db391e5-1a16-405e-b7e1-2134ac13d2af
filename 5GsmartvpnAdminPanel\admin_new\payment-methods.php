<?php
/**
 * Admin Panel - Payment Methods Configuration
 * Configure available payment methods for custom ads
 */

session_start();
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username'])) {
        header('Location: login.php');
        exit();
    }
}

$page_title = "Payment Methods";
$success = '';
$error = '';

// Add Bootstrap CSS and JS for modals
$additional_css = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css'
];
$additional_js = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'
];

// Handle payment method actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_method'])) {
        $method_code = trim($_POST['method_code']);
        $display_name = trim($_POST['display_name']);
        $account_number = trim($_POST['account_number']);
        $account_name = trim($_POST['account_name']);
        $instructions = trim($_POST['instructions']);
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        $display_order = (int)$_POST['display_order'];
        
        $stmt = $conn->prepare("INSERT INTO payment_methods (method_code, display_name, account_number, account_name, instructions, is_active, display_order) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("sssssii", $method_code, $display_name, $account_number, $account_name, $instructions, $is_active, $display_order);
        
        if ($stmt->execute()) {
            $success = "Payment method added successfully!";
        } else {
            $error = "Failed to add payment method.";
        }
    }
    
    if (isset($_POST['update_method'])) {
        $method_id = (int)$_POST['method_id'];
        $method_code = trim($_POST['method_code']);
        $display_name = trim($_POST['display_name']);
        $account_number = trim($_POST['account_number']);
        $account_name = trim($_POST['account_name']);
        $instructions = trim($_POST['instructions']);
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        $display_order = (int)$_POST['display_order'];
        
        $stmt = $conn->prepare("UPDATE payment_methods SET method_code = ?, display_name = ?, account_number = ?, account_name = ?, instructions = ?, is_active = ?, display_order = ? WHERE id = ?");
        $stmt->bind_param("sssssiii", $method_code, $display_name, $account_number, $account_name, $instructions, $is_active, $display_order, $method_id);
        
        if ($stmt->execute()) {
            $success = "Payment method updated successfully!";
        } else {
            $error = "Failed to update payment method.";
        }
    }
    
    if (isset($_POST['delete_method'])) {
        $method_id = (int)$_POST['method_id'];
        
        $stmt = $conn->prepare("DELETE FROM payment_methods WHERE id = ?");
        $stmt->bind_param("i", $method_id);
        
        if ($stmt->execute()) {
            $success = "Payment method deleted successfully!";
        } else {
            $error = "Failed to delete payment method.";
        }
    }
}

// Get payment methods
$methods_query = "SELECT * FROM payment_methods ORDER BY display_order ASC, display_name ASC";
$methods_result = mysqli_query($conn, $methods_query);
$methods = [];
if ($methods_result) {
    $methods = mysqli_fetch_all($methods_result, MYSQLI_ASSOC);
}

// Get payment statistics
$stats_query = "
    SELECT 
        pm.display_name,
        COUNT(cp.id) as payment_count,
        SUM(CASE WHEN cp.payment_status = 'verified' THEN cp.amount ELSE 0 END) as total_amount
    FROM payment_methods pm
    LEFT JOIN customer_payments cp ON pm.method_code = cp.payment_method
    WHERE pm.is_active = 1
    GROUP BY pm.id, pm.display_name
    ORDER BY total_amount DESC
";
$stats_result = mysqli_query($conn, $stats_query);
$payment_stats = [];
if ($stats_result) {
    $payment_stats = mysqli_fetch_all($stats_result, MYSQLI_ASSOC);
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Payment Methods</h1>
                <p class="page-subtitle">Configure available payment methods</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMethodModal">
                        <i class="ri-add-line me-1"></i>Add Payment Method
                    </button>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="ri-check-line me-2"></i><?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="ri-error-warning-line me-2"></i><?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Payment Statistics -->
            <?php if (!empty($payment_stats)): ?>
                <div class="dashboard-card mb-4">
                    <div class="card-header">
                        <h3 class="card-title">Payment Method Statistics</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($payment_stats as $stat): ?>
                                <div class="col-md-3 mb-3">
                                    <div class="stat-card-small">
                                        <h5><?php echo htmlspecialchars($stat['display_name']); ?></h5>
                                        <p class="mb-1"><strong><?php echo $stat['payment_count']; ?></strong> payments</p>
                                        <p class="mb-0 text-success"><strong>৳<?php echo number_format($stat['total_amount'], 2); ?></strong></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Payment Methods Table -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">Payment Methods</h3>
                </div>
                <div class="card-body">
                    <?php if (empty($methods)): ?>
                        <div class="text-center py-4">
                            <i class="ri-bank-card-line text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">No payment methods configured</h5>
                            <p class="text-muted">Add payment methods to allow customers to make payments.</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMethodModal">
                                <i class="ri-add-line me-1"></i>Add First Payment Method
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Method</th>
                                        <th>Account Details</th>
                                        <th>Instructions</th>
                                        <th>Status</th>
                                        <th>Order</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($methods as $method): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($method['display_name']); ?></strong>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($method['method_code']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($method['account_number']); ?></strong>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($method['account_name']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <small><?php
                                                    $instructions = $method['instructions'] ?? '';
                                                    echo htmlspecialchars(substr($instructions, 0, 100));
                                                    if (strlen($instructions) > 100): ?>...<?php endif;
                                                ?></small>
                                            </td>
                                            <td>
                                                <?php if ($method['is_active']): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $method['display_order']; ?></span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="editMethod(<?php echo htmlspecialchars(json_encode($method)); ?>)">
                                                        <i class="ri-edit-line"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="deleteMethod(<?php echo $method['id']; ?>, '<?php echo htmlspecialchars($method['display_name']); ?>')">
                                                        <i class="ri-delete-line"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Add Payment Method Modal -->
<div class="modal fade" id="addMethodModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Payment Method</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Method Code</label>
                            <select class="form-select" name="method_code" required>
                                <option value="">Select Method</option>
                                <option value="bkash">bKash</option>
                                <option value="nagad">Nagad</option>
                                <option value="rocket">Rocket</option>
                                <option value="upay">Upay</option>
                                <option value="google_pay">Google Pay</option>
                                <option value="paypal">PayPal</option>
                                <option value="bank_transfer">Bank Transfer</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Display Name</label>
                            <input type="text" class="form-control" name="display_name" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Account Number</label>
                            <input type="text" class="form-control" name="account_number" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Account Name</label>
                            <input type="text" class="form-control" name="account_name" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Payment Instructions</label>
                        <textarea class="form-control" name="instructions" rows="4"
                                  placeholder="Enter detailed instructions for customers..."></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Display Order</label>
                            <input type="number" class="form-control" name="display_order" min="0" value="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                                <label class="form-check-label" for="is_active">Active</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="add_method" class="btn btn-primary">Add Method</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Payment Method Modal -->
<div class="modal fade" id="editMethodModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Payment Method</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editMethodForm">
                <input type="hidden" name="method_id" id="edit_method_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Method Code</label>
                            <select class="form-select" name="method_code" id="edit_method_code" required>
                                <option value="">Select Method</option>
                                <option value="bkash">bKash</option>
                                <option value="nagad">Nagad</option>
                                <option value="rocket">Rocket</option>
                                <option value="upay">Upay</option>
                                <option value="google_pay">Google Pay</option>
                                <option value="paypal">PayPal</option>
                                <option value="bank_transfer">Bank Transfer</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Display Name</label>
                            <input type="text" class="form-control" name="display_name" id="edit_display_name" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Account Number</label>
                            <input type="text" class="form-control" name="account_number" id="edit_account_number" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Account Name</label>
                            <input type="text" class="form-control" name="account_name" id="edit_account_name" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Payment Instructions</label>
                        <textarea class="form-control" name="instructions" id="edit_instructions" rows="4"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Display Order</label>
                            <input type="number" class="form-control" name="display_order" id="edit_display_order" min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" name="is_active" id="edit_is_active">
                                <label class="form-check-label" for="edit_is_active">Active</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="update_method" class="btn btn-primary">Update Method</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editMethod(method) {
    document.getElementById('edit_method_id').value = method.id;
    document.getElementById('edit_method_code').value = method.method_code;
    document.getElementById('edit_display_name').value = method.display_name;
    document.getElementById('edit_account_number').value = method.account_number;
    document.getElementById('edit_account_name').value = method.account_name;
    document.getElementById('edit_instructions').value = method.instructions;
    document.getElementById('edit_display_order').value = method.display_order;
    document.getElementById('edit_is_active').checked = method.is_active == 1;

    new bootstrap.Modal(document.getElementById('editMethodModal')).show();
}

function deleteMethod(methodId, methodName) {
    if (confirm('Are you sure you want to delete the payment method "' + methodName + '"?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type="hidden" name="method_id" value="' + methodId + '">' +
                        '<input type="hidden" name="delete_method" value="1">';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<style>
/* Stat cards styling */
.stat-card-small {
    background: #f8fafc;
    border-radius: 8px;
    padding: 1rem;
    border-left: 4px solid #3b82f6;
}

.stat-card-small h5 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #111827;
}

.stat-card-small p {
    margin: 0;
    font-size: 0.875rem;
    color: #6b7280;
}

/* Dashboard card styling */
.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
}

/* Button improvements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-group-sm .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    min-width: 32px;
    min-height: 32px;
}

/* Table improvements */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

/* Alert improvements */
.alert {
    border-radius: 8px;
    border: none;
    margin-bottom: 1.5rem;
}

/* Badge improvements */
.badge {
    font-weight: 500;
    padding: 0.375rem 0.75rem;
}

/* Form improvements */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .header-actions {
        width: 100%;
    }

    .modal-dialog {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
}
</style>

<?php include 'includes/footer.php'; ?>
