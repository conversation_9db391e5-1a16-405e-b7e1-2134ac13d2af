<?php
/**
 * Database Migration Runner
 * This script safely runs the database migration to fix custom ads system issues
 * Run this once to resolve the database-related errors
 */

// Include database connection
require_once 'includes/config.php';

// Set execution time limit for migration
set_time_limit(300); // 5 minutes

// Function to execute SQL file
function executeSQLFile($conn, $filename) {
    if (!file_exists($filename)) {
        return ['success' => false, 'message' => "SQL file not found: $filename"];
    }
    
    $sql = file_get_contents($filename);
    if ($sql === false) {
        return ['success' => false, 'message' => "Could not read SQL file: $filename"];
    }
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $results = [];
    $errors = [];
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue; // Skip empty statements and comments
        }
        
        // <PERSON>le prepared statements differently
        if (strpos($statement, 'SET @sql') !== false || 
            strpos($statement, 'PREPARE stmt') !== false || 
            strpos($statement, 'EXECUTE stmt') !== false || 
            strpos($statement, 'DEALLOCATE PREPARE') !== false) {
            
            $result = mysqli_query($conn, $statement);
            if (!$result) {
                $errors[] = "Error in statement: " . mysqli_error($conn);
            } else {
                $results[] = "Executed: " . substr($statement, 0, 50) . "...";
            }
        } else {
            $result = mysqli_query($conn, $statement);
            if (!$result) {
                $errors[] = "Error in statement: " . mysqli_error($conn);
            } else {
                $results[] = "Executed: " . substr($statement, 0, 50) . "...";
            }
        }
    }
    
    return [
        'success' => empty($errors),
        'message' => empty($errors) ? 'Migration completed successfully' : 'Migration completed with errors',
        'results' => $results,
        'errors' => $errors
    ];
}

// Check if this is a POST request (form submission)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_migration'])) {
    $result = executeSQLFile($conn, 'database_migration_fix.sql');
    $migration_result = $result;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Migration - 5G Smart VPN</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="ri-database-2-line me-2"></i>
                            Database Migration Tool
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if (isset($migration_result)): ?>
                            <div class="alert alert-<?php echo $migration_result['success'] ? 'success' : 'danger'; ?> alert-dismissible fade show">
                                <h5 class="alert-heading">
                                    <i class="ri-<?php echo $migration_result['success'] ? 'check' : 'error-warning'; ?>-line me-2"></i>
                                    <?php echo $migration_result['message']; ?>
                                </h5>
                                
                                <?php if (!empty($migration_result['results'])): ?>
                                    <hr>
                                    <h6>Executed Statements:</h6>
                                    <ul class="mb-0">
                                        <?php foreach ($migration_result['results'] as $result): ?>
                                            <li><small><?php echo htmlspecialchars($result); ?></small></li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php endif; ?>
                                
                                <?php if (!empty($migration_result['errors'])): ?>
                                    <hr>
                                    <h6 class="text-danger">Errors:</h6>
                                    <ul class="mb-0">
                                        <?php foreach ($migration_result['errors'] as $error): ?>
                                            <li class="text-danger"><small><?php echo htmlspecialchars($error); ?></small></li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php endif; ?>
                                
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mb-4">
                            <h5>Migration Purpose</h5>
                            <p>This migration will fix the following database issues:</p>
                            <ul>
                                <li><strong>customer-accounts.php:</strong> Missing <code>created_at</code> column in custom_ads table</li>
                                <li><strong>custom-ads-analytics.php:</strong> Missing <code>created_at</code> and <code>is_approved</code> columns</li>
                                <li><strong>payment-methods.php:</strong> Null value handling for account information</li>
                            </ul>
                        </div>
                        
                        <div class="mb-4">
                            <h5>What This Migration Does</h5>
                            <ul>
                                <li>Creates missing tables: <code>customer_accounts</code>, <code>customer_payments</code>, <code>payment_methods</code>, <code>ad_packages</code></li>
                                <li>Adds missing columns to <code>custom_ads</code> table safely</li>
                                <li>Inserts default payment methods and ad packages</li>
                                <li>Updates existing records with proper timestamps</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="ri-information-line me-2"></i>
                            <strong>Important:</strong> This migration is safe to run multiple times. It will only add missing tables and columns without affecting existing data.
                        </div>
                        
                        <form method="POST" onsubmit="return confirm('Are you sure you want to run the database migration?');">
                            <div class="d-grid gap-2">
                                <button type="submit" name="run_migration" class="btn btn-primary btn-lg">
                                    <i class="ri-play-line me-2"></i>
                                    Run Database Migration
                                </button>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="ri-arrow-left-line me-2"></i>
                                    Back to Admin Panel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
