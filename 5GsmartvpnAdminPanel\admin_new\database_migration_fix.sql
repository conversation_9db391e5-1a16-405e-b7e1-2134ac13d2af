-- Database Migration Script to Fix Custom Ads System Issues
-- This script safely adds missing columns and tables to fix the reported errors
-- Run this script to resolve database-related issues

-- 1. Create customer_accounts table if it doesn't exist
CREATE TABLE IF NOT EXISTS customer_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    whatsapp_number VARCHAR(20) UNIQUE NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    total_spent DECIMAL(10,2) DEFAULT 0.00,
    total_orders INT DEFAULT 0,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP NULL,
    verification_code VARCHAR(6),
    verification_expires TIMESTAMP NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    INDEX idx_whatsapp (whatsapp_number),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Create customer_payments table if it doesn't exist
CREATE TABLE IF NOT EXISTS customer_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    package_id INT NULL,
    transaction_id VARCHAR(100) NOT NULL,
    payment_method ENUM('bkash', 'nagad', 'rocket', 'google_pay', 'paypal', 'manual') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'BDT',
    payment_status ENUM('pending', 'verified', 'rejected', 'refunded') DEFAULT 'pending',
    payment_proof TEXT,
    admin_notes TEXT,
    verified_by INT NULL,
    verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_customer (customer_id),
    INDEX idx_status (payment_status),
    INDEX idx_method (payment_method),
    INDEX idx_transaction (transaction_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Create payment_methods table if it doesn't exist
CREATE TABLE IF NOT EXISTS payment_methods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    method_code VARCHAR(20) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    account_number VARCHAR(100) NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    instructions TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_code (method_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Create ad_packages table if it doesn't exist
CREATE TABLE IF NOT EXISTS ad_packages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    package_name VARCHAR(100) NOT NULL,
    duration_days INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'BDT',
    max_ads INT DEFAULT 1,
    features JSON,
    is_active BOOLEAN DEFAULT TRUE,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_duration (duration_days)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. Add missing columns to custom_ads table safely
-- Check if columns exist before adding them to avoid errors

-- Add customer_id column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'custom_ads' 
     AND column_name = 'customer_id' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE custom_ads ADD COLUMN customer_id INT NULL',
    'SELECT "customer_id column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add created_at column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'custom_ads' 
     AND column_name = 'created_at' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE custom_ads ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
    'SELECT "created_at column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add updated_at column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'custom_ads' 
     AND column_name = 'updated_at' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE custom_ads ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
    'SELECT "updated_at column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add is_approved column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'custom_ads' 
     AND column_name = 'is_approved' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE custom_ads ADD COLUMN is_approved BOOLEAN DEFAULT NULL',
    'SELECT "is_approved column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add package_id column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'custom_ads' 
     AND column_name = 'package_id' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE custom_ads ADD COLUMN package_id INT NULL',
    'SELECT "package_id column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add payment_id column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'custom_ads' 
     AND column_name = 'payment_id' 
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE custom_ads ADD COLUMN payment_id INT NULL',
    'SELECT "payment_id column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. Insert default payment methods if table is empty
INSERT IGNORE INTO payment_methods (method_code, display_name, account_number, account_name, instructions, is_active, display_order) VALUES
('bkash', 'bKash Mobile Banking', '***********', '5G Smart VPN', 'Send money to our bKash number and provide the transaction ID', TRUE, 1),
('nagad', 'Nagad Mobile Banking', '***********', '5G Smart VPN', 'Send money to our Nagad number and provide the transaction ID', TRUE, 2),
('rocket', 'Rocket Mobile Banking', '***********', '5G Smart VPN', 'Send money to our Rocket number and provide the transaction ID', TRUE, 3),
('google_pay', 'Google Pay', '<EMAIL>', '5G Smart VPN', 'Send payment to our Google Pay account and include your WhatsApp number', TRUE, 4),
('paypal', 'PayPal', '<EMAIL>', '5G Smart VPN', 'Send payment to our PayPal account and include your WhatsApp number', TRUE, 5);

-- 7. Insert default ad packages if table is empty
INSERT IGNORE INTO ad_packages (package_name, duration_days, price, currency, max_ads, features, is_active, display_order) VALUES
('Basic Ad', 7, 500.00, 'BDT', 1, '{"description": "Basic ad package for 7 days", "features": ["Standard placement", "Basic analytics"]}', TRUE, 1),
('Premium Ad', 15, 1000.00, 'BDT', 1, '{"description": "Premium ad package for 15 days", "features": ["Priority placement", "Advanced analytics", "Featured listing"]}', TRUE, 2),
('Extended Ad', 30, 1800.00, 'BDT', 1, '{"description": "Extended ad package for 30 days", "features": ["Top placement", "Detailed analytics", "Featured listing", "Social media promotion"]}', TRUE, 3);

-- 8. Update existing custom_ads records to have proper timestamps if they don't
UPDATE custom_ads SET created_at = NOW() WHERE created_at IS NULL;
UPDATE custom_ads SET updated_at = NOW() WHERE updated_at IS NULL;

-- Success message
SELECT 'Database migration completed successfully!' as status;
